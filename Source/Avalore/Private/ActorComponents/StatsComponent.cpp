// Copyright (c) 2024, <PERSON>. All rights reserved.


#include "ActorComponents/StatsComponent.h"

#include "Entities/BaseEntity.h"
#include "Net/UnrealNetwork.h"


UStatsComponent::UStatsComponent()
{
	PrimaryComponentTick.bCanEverTick = true;

	PopulateStatsArray(); // Populate the Stats array with default stat entries
}

void UStatsComponent::BeginPlay()
{
	Super::BeginPlay();

	OwningEntity = Cast<ABaseEntity>(GetOwner());
}


void UStatsComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
}

void UStatsComponent::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(UStatsComponent, Stats); // Replicates the Stats variable to all Clients
}

void UStatsComponent::PopulateStatsArray()
{
	Stats.Reserve(StaticEnum<EStat>()->NumEnums() - 1); // Reserve space for all Enum Values (EStat) except 'None'

	Stats.Add(FStat{EStat::HealthRegeneration});
	Stats.Add(FStat{EStat::ManaRegeneration});
	Stats.Add(FStat{EStat::StaminaRegeneration});
	Stats.Add(FStat{EStat::AttackValue});
	Stats.Add(FStat{EStat::AbilityPower});
	Stats.Add(FStat{EStat::AttackSpeed});
	Stats.Add(FStat{EStat::Armor});
	Stats.Add(FStat{EStat::Spellguard});
	Stats.Add(FStat{EStat::MovementSpeed});
	Stats.Add(FStat{EStat::AttackRange});
	Stats.Add(FStat{EStat::AbilityHaste});
	Stats.Add(FStat{EStat::Leech});
	Stats.Add(FStat{EStat::CriticalChance});
	Stats.Add(FStat{EStat::CriticalDamage});
}

void UStatsComponent::OnRep_Stats()
{
	OnStatUpdated.Broadcast(); // Broadcast the fact that a stat has been updated so that any listeners can react to the change
}

void UStatsComponent::SetStatValue(EStat Stat, EStatComponent Component, float Value)
{
	if (!GetOwner()->HasAuthority())
		return;

	// Convert Enum to Array Index
	int32 StatIndex = static_cast<int32>(Stat);

	// Validate Array Bounds
	if (!Stats.IsValidIndex(StatIndex))
	{
		return;
	}

	// Verify Stat Type Consistency
	if (Stats[StatIndex].Stat != Stat)
		return;

	// Convert Component Enum to Index
	int32 ComponentIndex = static_cast<int32>(Component);

	// Validate Component Array Bounds
	if (!Stats[StatIndex].Components.IsValidIndex(ComponentIndex))
		return;

	// Set the Stat Component Value
	Stats[StatIndex].Components[ComponentIndex] = Value;

	// Broadcast the Stat Update Event
	OnStatUpdated.Broadcast();
}

float UStatsComponent::GetStatValue(EStat Stat, EStatComponent Component, bool bIgnoreComponentAndReturnTotal) const
{
	// Convert Stat Enum to Array Index
	int32 StatIndex = static_cast<int32>(Stat);

	// Validate Array Bounds
	if (!Stats.IsValidIndex(StatIndex))
		return 0.0f;

	// Verify Stat Type Consistency
	if (Stats[StatIndex].Stat != Stat)
		return 0.0f;

	// Check if we should return total value instead of specific component only
	if (bIgnoreComponentAndReturnTotal)
	{
		float Total = 0.0f;
		
		for (int32 i = 0; i < Stats[StatIndex].Components.Num(); ++i)
		{
			Total += Stats[StatIndex].Components[i];
		}
		return Total;
	}

	// Return Specific Component Value
	int32 ComponentIndex = static_cast<int32>(Component);

	// Validate Component Array Bounds
	if (!Stats[StatIndex].Components.IsValidIndex(ComponentIndex))
		return 0.0f;

	// Return the Requested Component Value
	return Stats[StatIndex].Components[ComponentIndex];
}

void UStatsComponent::IncreaseStatValue(EStat Stat, EStatComponent Component, float Value)
{
	if (!GetOwner()->HasAuthority())
		return;

	// Get Current Value
	float CurrentValue = GetStatValue(Stat, Component, false);

	// Calculate New Value
	float NewValue = CurrentValue + Value;

	// Set the New Value
	SetStatValue(Stat, Component, NewValue);
}
