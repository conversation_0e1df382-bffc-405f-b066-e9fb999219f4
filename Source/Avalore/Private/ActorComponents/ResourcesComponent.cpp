// Copyright (c) 2024, <PERSON>. All rights reserved.


#include "ActorComponents/ResourcesComponent.h"

#include "Entities/BaseEntity.h"
#include "Net/UnrealNetwork.h"

#include "ActorComponents/StatsComponent.h"


UResourcesComponent::UResourcesComponent()
{
	PrimaryComponentTick.bCanEverTick = true;

	PopulateResourcesMap(); // Populate the Resources map with default resource entries
}

void UResourcesComponent::BeginPlay()
{
	Super::BeginPlay();

	OwningEntity = Cast<ABaseEntity>(GetOwner());

	// Start the timer for the ResourcesRegenerationTick() function which runs every second. Runs only on the Server!
	if (GetOwner()->HasAuthority())
	{
		GetWorld()->GetTimerManager().SetTimer(ResourcesRegenerationTickTimerHandle, this, &UResourcesComponent::ResourcesRegenerationTick, 1.0f, true);
	}
}

void UResourcesComponent::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	// Clear the timer for the ResourcesRegenerationTick() function
	if (ResourcesRegenerationTickTimerHandle.IsValid())
	{
		GetWorld()->GetTimerManager().ClearTimer(ResourcesRegenerationTickTimerHandle);
	}

	Super::EndPlay(EndPlayReason);
}

void UResourcesComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
}

void UResourcesComponent::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(UResourcesComponent, Resources); // Replicates the Resources variable to all Clients
}

void UResourcesComponent::OnRep_Resources()
{
	OnResourceUpdated.Broadcast(); // Broadcast the fact that a resource has been updated so that any listeners can react to the change
}

/**
 * Sets the value of a specified resource's component to a given value.
 *
 * @param Resource The resource type to be modified (e.g., Health, Mana, Stamina, etc.).
 * @param Component The specific component of the resource to modify (e.g., Base, Attributed, Itemized, etc.).
 * @param Value The new value to set for the specified resource component.
 */
void UResourcesComponent::SetResourceValue(EResource Resource, EResourceComponent Component, float Value)
{
	if (!GetOwner()->HasAuthority())
	{
		return;
	}

	// Convert Resource Enum to Array Index
	// The Resource array is indexed by the EResource enum (e.g., Health = 0, Mana = 1, etc.) so cast the Enum to int32 to use it as an array index
	int32 ResourceIndex = static_cast<int32>(Resource);

	// Validate Array Bounds
	// Check if the calculated index is within the valid range of our Resources array. This prevents crashes from accessing invalid memory locations.
	if (!Resources.IsValidIndex(ResourceIndex))
	{
		return;
	}

	// Verify Resource Type Consistency
	// Double-check that the resource stored at this index matches what we expect. This is a safety measure in case the array gets corrupted or misordered.
	if (Resources[ResourceIndex].Resource != Resource)
		return;

	// Convert Component Enum to Index
	// Each Resource has 5 components: Base (0), Attributed (1), Itemized (2), Other (3), Current (4).
	int32 ComponentIndex = static_cast<int32>(Component);

	// Validate Component Array Bounds
	// Ensure the component index is valid within the Components array (should be 0-4). This prevents accessing invalid component data.
	if (!Resources[ResourceIndex].Components.IsValidIndex(ComponentIndex))
		return;

	// Set the Resource Component Value
	// Update the value of the specific resource component with the new value.
	Resources[ResourceIndex].Components[ComponentIndex] = Value;

	// Broadcast the Resource Update Event
	// Notify other components or systems that a resource has been updated. This could trigger UI updates, stat recalculations, etc.
	OnResourceUpdated.Broadcast();

	// Note that the Resources array is marked as Replicated, so this change will automatically be sent to all Clients from the Server.
	// When Clients receive it, the OnRep_Resources() function will be called, which will broadcast the OnResourceUpdated event.
}

/**
 * Retrieves the value of a specific resource component or the total value of the resource.
 *
 * @param Resource The resource type from which to retrieve the value (e.g., Health, Mana, Stamina, etc.).
 * @param Component The specific component of the resource to retrieve (e.g., Base, Attributed, Itemized, etc.).
 * @param bIgnoreComponentAndReturnTotal If true, calculates and returns the total value of the resource
 *        (excluding the current component), disregarding the specified component.
 * @return The integer value of the specified resource component or the total resource value if
 *         bIgnoreComponentAndReturnTotal is true. Returns 0 if the input parameters are invalid.
 */
float UResourcesComponent::GetResourceValue(EResource Resource, EResourceComponent Component, bool bIgnoreComponentAndReturnTotal) const
{
	// Convert Resources Enum to Array Index
	// The Resources array is indexed by the EResource enum (e.g., Health = 0, Mana = 1, etc.) so cast the Enum to int32 to use it as an array index
	int32 ResourceIndex = static_cast<int32>(Resource);

	// Validate Array Bounds
	// Check if the calculated index is within the valid range of our Resources array. This prevents crashes from accessing invalid memory locations.
	if (!Resources.IsValidIndex(ResourceIndex))
		return 0.0f;

	// Verify Resource Type Consistency
	// Double-check that the resource stored at this index matches what we expect. This is a safety measure in case the array gets corrupted or misordered.
	if (Resources[ResourceIndex].Resource != Resource)
		return 0.0f;

	// Check if Total Value should be returned
	// If 'bIgnoreComponentAndReturnTotal' is true, we should return the total value of the resource (sum of all its components, aka 'Base' + 'Attributed' + 'Itemized' + 'Other'; excluding 'Current')
	if (bIgnoreComponentAndReturnTotal)
	{
		float Total = 0.0f;
		for (int32 i = 0; i < Resources[ResourceIndex].Components.Num() - 1; ++i) // Here needs to be -1 because we don't want to include the 'Current' component in the total value
		{
			if (Resources[ResourceIndex].Components.IsValidIndex(i))
			{
				Total += Resources[ResourceIndex].Components[i];
			}
		}
		return Total;
	}

	// Return Specific Component Value
	// Convert the EResourceComponent Enum to an array index
	int32 ComponentsIndex = static_cast<int32>(Component);

	// Validate Component Array Bounds
	// Ensure the Component index is valid within the Components array (should be 0-4). This prevents accessing invalid component data.
	if (!Resources[ResourceIndex].Components.IsValidIndex(ComponentsIndex))
		return 0.0f;

	// Return the Requested Component Value
	// Return the specific component value of the resource (Base, Attributed, Itemized, Other, or Current)
	return Resources[ResourceIndex].Components[ComponentsIndex];
}

void UResourcesComponent::ServerSetResourceValue_Implementation(EResource Resource, EResourceComponent Component, float Value)
{
	if (!GetOwner()->HasAuthority())
		return;

	SetResourceValue(Resource, Component, Value);
}

/**
 * Increases the value of a specified resource's component by a given amount.
 *
 * @param Resource The resource type to be modified (e.g., Health, Mana, Stamina, etc.).
 * @param Component The specific component of the resource to modify (e.g., Base, Attributed, Itemized, etc.).
 * @param Value The amount to increase the value of the specified resource component by.
 */
void UResourcesComponent::IncreaseResourceValue(EResource Resource, EResourceComponent Component, float Value)
{
	if (!GetOwner()->HasAuthority())
		return;

	float CurrentValue = GetResourceValue(Resource, Component, false);
	
	float NewValue = CurrentValue + Value;
	
	SetResourceValue(Resource, Component, NewValue);
}

/**
 * Checks if the specified resource is full by comparing its current value to the total value.
 *
 * @param Resource The specific resource to check (e.g., Health, Mana, Stamina, etc.).
 * @return True if the resource is full (current value is greater than or equal to the total value), false otherwise.
 */
bool UResourcesComponent::IsResourceFull(EResource Resource) const
{
	// Get Current Resource Value
	// This is the actual amount of the resource that is currently available/remaining 
	float CurrentValue = GetResourceValue(Resource, EResourceComponent::Current, false);

	// Get Total Resource Value (excluding the current component)
	// This is the sum of all the resource components (Base, Attributed, Itemized, Other)
	// We use the 'true' parameter to ignore the specific component and return the total value
	// We use 'Other' as the component because it's the last one in the array and it's convenient (not obligatory tho!)
	float MaxValue = GetResourceValue(Resource, EResourceComponent::Other, true);

	// Compare Current Value to Max Value
	// Resource is considered full if the Current Value is greater than or equal to the Max Value
	return CurrentValue >= MaxValue;
}

void UResourcesComponent::ServerIncreaseResourceValue_Implementation(EResource Resource, EResourceComponent Component, float Value)
{
	if (!GetOwner()->HasAuthority())
		return;

	IncreaseResourceValue(Resource, Component, Value);
}

bool UResourcesComponent::SubtractHealth(float Value)
{
	if (!GetOwner()->HasAuthority())
		return false;

	// Get Current Health
	float CurrentHealth = GetResourceValue(EResource::Health, EResourceComponent::Current, false);

	// Calculate New Health
	float NewHealth = CurrentHealth - Value;

	// Check if the subtraction applied a lethal (entity dies) by determining if the new health is less than or equal to 0
	bool bIsLethal = NewHealth <= 0.0f;

	// Clamp to prevent negative health
	NewHealth = FMath::Max(NewHealth, 0.0f);

	// Set the new health
	SetResourceValue(EResource::Health, EResourceComponent::Current, NewHealth);

	// Return whether the subtraction was lethal
	return bIsLethal;
}

void UResourcesComponent::SubtractMana(float Value)
{
	if (!GetOwner()->HasAuthority())
		return;

	float CurrentMana = GetResourceValue(EResource::Mana, EResourceComponent::Current, false);

	float NewMana = CurrentMana - Value;

	NewMana = FMath::Max(NewMana, 0.0f);

	SetResourceValue(EResource::Mana, EResourceComponent::Current, NewMana);
}

void UResourcesComponent::SubtractStamina(float Value)
{
	if (!GetOwner()->HasAuthority())
		return;

	float CurrentStamina = GetResourceValue(EResource::Stamina, EResourceComponent::Current, false);

	float NewStamina = CurrentStamina - Value;

	NewStamina = FMath::Max(NewStamina, 0.0f);

	SetResourceValue(EResource::Stamina, EResourceComponent::Current, NewStamina);
}

void UResourcesComponent::ScaleCurrentValueToNewMaxValue(EResource Resource)
{
	if (!GetOwner()->HasAuthority())
		return;

	// Get Total Value (excluding the current component); This is the sum of all the resource components (Base, Attributed, Itemized, Other)
	float TotalValue = GetResourceValue(Resource, EResourceComponent::Other, true);

	// Set Current Value to match Total Value
	SetResourceValue(Resource, EResourceComponent::Current, TotalValue);
}

bool UResourcesComponent::ShouldScaleCurrentValueToNewMaxValue(EResource Resource) const
{
	// Get Current Value
	// This is the actual amount of the resource that is currently available/remaining 
	float CurrentValue = GetResourceValue(Resource, EResourceComponent::Current, false);

	// Get Max Value (excluding the current component)
	// This is the sum of all the resource components (Base, Attributed, Itemized, Other)
	float MaxValue = GetResourceValue(Resource, EResourceComponent::Other, true);

	// Check if Current Value is greater than Max Value
	// If it is, we should scale the current value to the new Max Value
	return CurrentValue > MaxValue;
}

float UResourcesComponent::GetCurrentHealthAsPercentageForProgressBar() const
{
	// Get Current Health
	// This is the actual amount of the resource that is currently available/remaining 
	float CurrentHealth = GetResourceValue(EResource::Health, EResourceComponent::Current, false);

	// Get Max Health (excluding the current component)
	// This is the sum of all the resource components (Base, Attributed, Itemized, Other)
	float MaxHealth = GetResourceValue(EResource::Health, EResourceComponent::Other, true);

	// Calculate Percentage for ProgressBar
	// Divide Current by Max to get a value between 0.0 and 1.0
	// Handle division by zero by returning 0.0 if Max is 0
	if (MaxHealth > 0.0f)
	{
		return CurrentHealth / MaxHealth;
	}

	return 0.0f;
}

void UResourcesComponent::ResourcesRegenerationTick()
{
	if (!GetOwner()->HasAuthority())
		return;

	UStatsComponent* StatsComponent = OwningEntity->StatsComponent;
	if (!StatsComponent)
		return;

	if (!IsResourceFull(EResource::Health))
	{
		float HealthRegenValue = StatsComponent->GetStatValue(EStat::HealthRegeneration, EStatComponent::Other, true);

		IncreaseResourceValue(EResource::Health, EResourceComponent::Current, HealthRegenValue);
	}

	if (!IsResourceFull(EResource::Mana))
	{
		float ManaRegenValue = StatsComponent->GetStatValue(EStat::ManaRegeneration, EStatComponent::Other, true);

		IncreaseResourceValue(EResource::Mana, EResourceComponent::Current, ManaRegenValue);
	}

	if (!IsResourceFull(EResource::Stamina))
	{
		float StaminaRegenValue = StatsComponent->GetStatValue(EStat::StaminaRegeneration, EStatComponent::Other, true);

		IncreaseResourceValue(EResource::Stamina, EResourceComponent::Current, StaminaRegenValue);
	}
}
