// Copyright (c) 2024, <PERSON>. All rights reserved.


#include "Entities/BaseEntity.h"
#include "Entities/PlayerCharacters/PlayerCharacter.h"

#include "Net/UnrealNetwork.h"

#include "Utils/DebugUtils.h"
#include "UI/EntityNameplateWidget.h"
#include "Components/CapsuleComponent.h"

#include "ActorComponents/AttributesComponent.h"
#include "ActorComponents/ResourcesComponent.h"
#include "ActorComponents/StatsComponent.h"


// Constructor
ABaseEntity::ABaseEntity()
{
	PrimaryActorTick.bCanEverTick = true;

	// Creates the default subobjects for the Entity's Components
	AttributesComponent = CreateDefaultSubobject<UAttributesComponent>(TEXT("AttributesComponent"));
	ResourcesComponent = CreateDefaultSubobject<UResourcesComponent>(TEXT("ResourcesComponent"));
	StatsComponent = CreateDefaultSubobject<UStatsComponent>(TEXT("StatsComponent"));

	// Configures generic properties for the entity
	ConfigureGenericMeshProperties();

	// Configures collision properties for the entity's capsule component
	ConfigureCollisionProperties();

	// Creates the Nameplate's Widget Component and sets some properties
	CreateNameplateComponent();

	// Creates the Nameplate's Sphere Collision Component and sets some properties
	CreateNameplateSphere();
}

// Called when the game starts or when spawned
void ABaseEntity::BeginPlay()
{
	Super::BeginPlay();
	
	InitializeAssociatedEntityForNameplateWidget(); // Sets the associated entity for the Nameplate widget
	InitializeSpecificEntityCoreData(); // Initializes the core data struct for the specific entity (e.g. PlayerCharacterData for PlayerCharacters)
}

void ABaseEntity::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}

/**
 * Sets up input bindings for the entity.
 *
 * This method enables the entity to respond to player input by configuring the
 * associated input component with the necessary bindings. It also calls the
 * parent class's implementation to ensure proper initialization and propagation
 * of input configuration.
 *
 * @param PlayerInputComponent The input component where bindings are setup for player controls.
 */
void ABaseEntity::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
	Super::SetupPlayerInputComponent(PlayerInputComponent);
}

void ABaseEntity::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	// We need to always call this Super function to ensure that the base class properties are replicated correctly
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	// DOREPLIFETIME macro is used to replicate properties of this class to clients (there are also DOREPLIFETIME_CONDITION macros for conditional replication)
	DOREPLIFETIME(ABaseEntity, LifecycleState); // Replicates the LifecycleState variable to all clients
}

/**
 * Configures generic properties for the entity's skeletal mesh.
 *
 * Adjusts the mesh's position and orientation relative to the entity's root component
 * to ensure proper alignment in the game world.
 *
 * Configuration details:
 * - Relative Location: Offsets the mesh 90 units below the entity's root component.
 * - Relative Rotation: Rotates the mesh -90 degrees along the yaw axis to align it accordingly.
 */
void ABaseEntity::ConfigureGenericMeshProperties()
{
	GetMesh()->SetRelativeLocation(FVector(0.0f, 0.0f, -90.0f));
	GetMesh()->SetRelativeRotation(FRotator(0.0f, -90.0f, 0.0f));
}

void ABaseEntity::ConfigureCollisionProperties()
{
	// Configure collision settings on the capsule component to match blueprint
	GetCapsuleComponent()->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
	GetCapsuleComponent()->SetCollisionObjectType(ECC_Pawn);

	// Set collision responses to match the blueprint image
	GetCapsuleComponent()->SetCollisionResponseToAllChannels(ECR_Ignore);
	GetCapsuleComponent()->SetCollisionResponseToChannel(ECC_Visibility, ECollisionResponse::ECR_Block);
	GetCapsuleComponent()->SetCollisionResponseToChannel(ECC_Camera, ECR_Ignore);
	GetCapsuleComponent()->SetCollisionResponseToChannel(ECC_WorldStatic, ECR_Block);
	GetCapsuleComponent()->SetCollisionResponseToChannel(ECC_WorldDynamic, ECR_Block);
	GetCapsuleComponent()->SetCollisionResponseToChannel(ECC_Pawn, ECR_Ignore);
	GetCapsuleComponent()->SetCollisionResponseToChannel(ECC_PhysicsBody, ECR_Block);
	GetCapsuleComponent()->SetCollisionResponseToChannel(ECC_Vehicle, ECR_Block);
	GetCapsuleComponent()->SetCollisionResponseToChannel(ECC_Destructible, ECR_Block);
}

/**
 * Creates and configures a widget component for displaying the entity's nameplate.
 *
 * Initializes the Nameplate as a UWidgetComponent attached to the root component.
 * The widget is set to display in screen space for visibility, with collision configured using a UI-specific
 * collision profile in query-only mode. Initially, the widget is set to be invisible.
 *
 * The nameplate's position is adjusted to appear above the entity by setting its relative location.
 *
 * Configuration details:
 * - WidgetSpace: Screen space for rendering.
 * - CollisionEnabled: Query-only to interact with collision queries without physical simulation.
 * - CollisionProfileName: Set to "UI" for appropriate interaction settings.
 * - Visibility: Initially disabled for controlled display logic.
 * - Relative Location: Positioned 100 units above the entity's default location.
 */
void ABaseEntity::CreateNameplateComponent()
{
	Nameplate = CreateDefaultSubobject<UWidgetComponent>(TEXT("Nameplate"));
	Nameplate->SetupAttachment(RootComponent);
	Nameplate->SetWidgetSpace(EWidgetSpace::Screen);
	Nameplate->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
	Nameplate->SetCollisionProfileName(TEXT("UI"));
	Nameplate->SetVisibility(false);

	Nameplate->SetRelativeLocation(FVector(0.0f, 0.0f, 100.0f)); // Sets the relative location of the nameplate widget above the entity
}

/**
 * Creates a spherical collision component to handle interactions related to the visibility of the entity's nameplate.
 *
 * Initializes the NameplateRange as a USphereComponent attached to the root component, setting its radius, collision
 * properties, and overlap responses. The sphere is configured to overlap with pawns and ignores other collision channels.
 * The method also binds dynamic event handlers for begin and end overlap events to manage nameplate visibility.
 *
 * The created sphere represents the area within which nameplates will display based on proximity to the actor.
 *
 * Collision configuration:
 * - Query-only collision enabled.
 * - Overlaps only with pawns (ignored for all other channels).
 *
 * Event bindings:
 * - OnComponentBeginOverlap: Triggers logic when an actor enters the sphere.
 * - OnComponentEndOverlap: Triggers logic when an actor leaves the sphere.
 */
void ABaseEntity::CreateNameplateSphere()
{
	NameplateRange = CreateDefaultSubobject<USphereComponent>(TEXT("NameplateCollision"));
	NameplateRange->SetupAttachment(RootComponent);
	NameplateRange->SetSphereRadius(2500.0f);
	NameplateRange->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
	NameplateRange->SetCollisionObjectType(ECC_WorldDynamic);
	NameplateRange->SetCollisionResponseToAllChannels(ECR_Ignore);
	NameplateRange->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);

	NameplateRange->SetGenerateOverlapEvents(true);

	// Binds the actuals functions with the logic that should happen upon Overlap/End Overlap
	NameplateRange->OnComponentBeginOverlap.AddDynamic(this, &ABaseEntity::OnNameplateRangeBeginOverlap);
	NameplateRange->OnComponentEndOverlap.AddDynamic(this, &ABaseEntity::OnNameplateRangeEndOverlap);
}

/**
 * Initializes the association between the entity and its corresponding Nameplate widget, enabling entity-specific logic
 * and broadcasting relevant events for updated initialization.
 *
 * This method ensures that the Nameplate component's widget object, if valid, is cast to the expected
 * `UEntityNameplateWidget` subclass. Upon a successful cast, it sets the associated entity reference within the widget
 * to this instance of `ABaseEntity`. Additionally, a broadcast is triggered through the `OnAssociatedEntityInitialized`
 * event, signaling that the widget has been successfully initialized.
 *
 * Logging is utilized to indicate the method's execution and debug potential issues, such as a null `NameplateWidget`
 * preventing event broadcasting.
 *
 * Preconditions:
 * - The Nameplate component must be valid and properly initialized.
 * - The Nameplate's widget object should be of the `UEntityNameplateWidget` type.
 *
 * Functionality steps:
 * - Check the validity of the Nameplate component.
 * - Attempt to retrieve the associated user widget object.
 * - Cast the widget object to `UEntityNameplateWidget`.
 * - Configure the widget by associating this entity instance.
 * - Trigger the `OnAssociatedEntityInitialized` delegate upon successful widget setup.
 *
 * Warnings:
 * - If the Nameplate or NameplateWidget is invalid, the method might fail silently, logging the issue but skipping event triggers.
 * - Ensure the widget class assigned to the Nameplate component supports the expected logic for association.
 */
void ABaseEntity::InitializeAssociatedEntityForNameplateWidget()
{
	AVALORE_NETWORKING_LOG(this, (TEXT("Called 'InitializeAssociatedEntityForNameplateWidget()'!")));

	if (Nameplate) // Checks if the Nameplate component is valid
	{
		UUserWidget* UserWidgetObject = Nameplate->GetUserWidgetObject(); // Retrieves the actual WBP from the Nameplate component

		if (UserWidgetObject)
		{
			// We cast into the specific WBP Nameplate (parent class in the C++) in order to access the function SetAssociatedEntity
			NameplateWidget = Cast<UEntityNameplateWidget>(UserWidgetObject);
			if (NameplateWidget)
			{
				NameplateWidget->SetAssociatedEntity(this); // Sets the associated entity (this class) for the WBP Nameplate)

				NameplateWidget->OnAssociatedEntityInitialized.Broadcast();
			}
			else
			{
				AVALORE_NETWORKING_LOG(this, (TEXT("NameplateWidget is null, cannot broadcast!")));
			}
		}
	}
}

float ABaseEntity::GetInteractionRange(const ABaseEntity* CallerEntity) const
{
	return CoreEntityData.InteractionRange; // Default behavior; should be overridden by derived classes if necessary, such as for Monsters, where their interaction range is based on the attackers/initiators attack range.
}

/**
 * Calculates the real distance between this entity and a target actor, accounting for their collision radii.
 *
 * This function determines the surface-to-surface distance between this entity and the specified target actor
 * by subtracting their collision radii from the center-to-center distance. If either this entity or the target
 * actor is invalid, the function returns -1.0f. The result is clamped to ensure non-negative distances.
 *
 * @param Target The target actor to calculate the distance to. Must be a valid AActor reference.
 * @return The real distance between the surfaces of this entity and the target actor.
 * Returns -1.0f if either this entity or the target actor is invalid.
 */
float ABaseEntity::CalculateRealDistanceToEntity(const AActor* Target) const
{
	if (!Target)
	{
		return -1.0f;
	}

	// Get the distance between actor centers
	float CenterDistance = FVector::Dist(GetActorLocation(), Target->GetActorLocation());

	// Subtract the collision radii to get the real distance between surfaces
	float ThisEntityRadius = 0.0f;
	float TargetRadius = 0.0f;

	if (UCapsuleComponent* ThisCapsule = GetCapsuleComponent())
	{
		ThisEntityRadius = ThisCapsule->GetScaledCapsuleRadius();
	}

	if (const UCapsuleComponent* TargetCapsule = Target->GetComponentByClass<UCapsuleComponent>())
	{
		TargetRadius = TargetCapsule->GetScaledCapsuleRadius();
	}

	// Calculate real distance (surface to surface)
	float RealDistance = CenterDistance - ThisEntityRadius - TargetRadius;

	// Ensure we don't return negative distances
	return FMath::Max(0.0f, RealDistance);
}

/**
 * Determines whether this entity is within interaction range of the specified target entity.
 *
 * This function calculates the real distance between this entity and the target entity using
 * surface-to-surface measurement (accounting for collision radii). It then compares this distance
 * against the target entity's interaction range to determine if interaction is possible.
 *
 * @param Target The target entity to check interaction range with. Must be a valid ABaseEntity reference.
 * @return True if this entity is within interaction range of the target, false otherwise. Returns false if
 *         the target entity is invalid, or if the target's interaction range is invalid.
 */
bool ABaseEntity::IsWithinInteractionRangeOf(const ABaseEntity* Target) const
{
	if (!Target)
	{
		return false;
	}

	// Get the real distance between this entity and the target (surface-to-surface)
	float RealDistance = CalculateRealDistanceToEntity(Target);

	// If distance calculation failed, we can't determine interaction range
	if (RealDistance < 0.0f)
	{
		return false;
	}

	// Get the interaction range from the target entity
	float InteractionRange = Target->GetInteractionRange(this);

	// If we don't have a valid interaction range, we can't interact
	if (InteractionRange <= 0.0f)
	{
		return false;
	}

	// Check if the real distance is within the interaction range
	return RealDistance <= InteractionRange;
}

/**
 * Default interaction behavior when this entity is interacted with by another entity.
 *
 * This virtual function provides the base implementation for entity interactions. By default,
 * it simply logs the interaction. Derived classes should override this function to implement
 * specific interaction behaviors such as dialogue for NPCs, combat for monsters, mining for
 * resources, etc.
 *
 * @param Initiator The entity that is initiating the interaction with this entity.
 *                          Can be used to determine context-specific behavior.
 */
void ABaseEntity::Interact(ABaseEntity* Initiator)
{
	if (!Initiator)
	{
		return;
	}

	// Default behavior - log the interaction
	AVALORE_STANDARD_LOG(TEXT("Entity %s was interacted with by %s"), *GetName(), *Initiator->GetName());

	// Derived classes should override this function to implement specific interaction logic:
	// - NPCs: Open dialogue, shop, or quest interfaces
	// - Monsters: Start combat or take damage
	// - Resources: Begin mining, harvesting, or collection
	// - Items: Pick up or examine
	// - Environmental objects: Activate, open, or use
}

/**
 * Server RPC to rotate this entity towards a target location.
 *
 * This function is called on the server to handle entity rotation towards a target.
 * It calculates the rotation needed to face the target and applies it to this entity.
 * The rotation is then replicated to all clients through the ClientRotateTowardsTarget RPC.
 * This can be used by any entity type (players, NPCs, monsters, etc.) to face their targets.
 *
 * @param TargetLocation The world location that this entity should rotate towards.
 */
void ABaseEntity::ServerRotateTowardsTarget_Implementation(const FVector& TargetLocation)
{
	// Calculate the rotation needed to face the target
	FVector EntityLocation = GetActorLocation();
	FVector DirectionToTarget = (TargetLocation - EntityLocation).GetSafeNormal();

	// Only rotate on the Z-axis (yaw) to keep the entity upright
	DirectionToTarget.Z = 0.0f;
	DirectionToTarget.Normalize();

	// Convert direction to rotation
	FRotator TargetRotation = DirectionToTarget.Rotation();

	// Apply the rotation to this entity
	SetActorRotation(TargetRotation);

	// Replicate the rotation to all clients
	ClientRotateTowardsTarget(TargetLocation);

	AVALORE_NETWORKING_LOG(this, (TEXT("Called 'ServerRotateTowardsTarget_Implementation()'!")));
}

/**
 * Client RPC to rotate this entity towards a target location.
 *
 * This function is called on clients to ensure the entity rotation is synchronized across
 * all connected players. It performs the same rotation calculation as the server version
 * to maintain consistency in the networked environment.
 *
 * @param TargetLocation The world location that this entity should rotate towards.
 */
void ABaseEntity::ClientRotateTowardsTarget_Implementation(const FVector& TargetLocation)
{
	// Calculate the rotation needed to face the target
	FVector EntityLocation = GetActorLocation();
	FVector DirectionToTarget = (TargetLocation - EntityLocation).GetSafeNormal();

	// Only rotate on the Z-axis (yaw) to keep the entity upright
	DirectionToTarget.Z = 0.0f;
	DirectionToTarget.Normalize();

	// Convert direction to rotation
	FRotator TargetRotation = DirectionToTarget.Rotation();

	// Apply the rotation to this entity
	SetActorRotation(TargetRotation);

	AVALORE_NETWORKING_LOG(this, (TEXT("Called 'ClientRotateTowardsTarget_Implementation()'!")));
}

/**
 * Helper function to rotate this entity towards another entity.
 *
 * This function initiates the rotation process by calling the appropriate network RPCs
 * to ensure this entity faces the target entity. It handles both server and client
 * scenarios properly for networked gameplay. Can be used by any entity type.
 *
 * @param TargetEntity The entity that this entity should rotate towards.
 */
void ABaseEntity::RotateTowardsEntity(ABaseEntity* TargetEntity)
{
	if (!TargetEntity)
	{
		return;
	}

	FVector TargetLocation = TargetEntity->GetActorLocation();

	// Call the server RPC to handle rotation on server and replicate to clients
	ServerRotateTowardsTarget(TargetLocation);
	AVALORE_NETWORKING_LOG(this, (TEXT("Called 'RotateTowardsEntity()'!")));
}

/**
 * Handles the logic triggered when another actor begins overlap with the NameplateRange component.
 * If the overlapping actor is a locally controlled player character, the nameplate visibility is set to true.
 *
 * @param OverlappedComponent The component that triggered the begin overlap event.
 * @param OtherActor The actor that has entered the overlap range.
 * @param OtherComp The specific component of the other actor involved in the overlap.
 * @param OtherBodyIndex The index of the body that has overlapped.
 * @param bFromSweep Indicates whether the overlap was caused by a sweep test.
 * @param SweepResult Contains information about the sweep test if bFromSweep is true.
 */
void ABaseEntity::OnNameplateRangeBeginOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor,
                                               UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
	if (APlayerCharacter* PlayerCharacter = Cast<APlayerCharacter>(OtherActor))
	{
		if (PlayerCharacter->IsLocallyControlled() && Nameplate)
		{
			Nameplate->SetVisibility(true);
		}
	}
}

/**
 * Handles the event when another actor ends overlap with the NameplateRange component.
 * Specifically, it hides the entity's nameplate if the other actor is a locally controlled
 * player character. This ensures that nameplates are only visible within the configured range.
 *
 * @param OverlappedComponent The component that triggered the end overlap event.
 * @param OtherActor The actor that exited the overlap range.
 * @param OtherComp The specific component of the other actor involved in the overlap.
 * @param OtherBodyIndex The index of the body that exited the overlap range.
 */
void ABaseEntity::OnNameplateRangeEndOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor,
                                             UPrimitiveComponent* OtherComp, int32 OtherBodyIndex)
{
	if (APlayerCharacter* PlayerCharacter = Cast<APlayerCharacter>(OtherActor))
	{
		if (PlayerCharacter->IsLocallyControlled() && Nameplate)
		{
			Nameplate->SetVisibility(false);
		}
	}
}