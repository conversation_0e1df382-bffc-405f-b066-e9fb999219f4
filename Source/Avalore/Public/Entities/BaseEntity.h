// Copyright (c) 2024, <PERSON>. All rights reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Character.h"
#include "Structs/AvaloreEnums.h"
#include "Structs/AvaloreStructs.h"

#include "Components/WidgetComponent.h"
#include "Components/SphereComponent.h"

#include "BaseEntity.generated.h"

class UAttributesComponent;
class UResourcesComponent;
class UStatsComponent;

UCLASS()
class AVALORE_API ABaseEntity : public ACharacter
{
	GENERATED_BODY()

public:
	ABaseEntity();

public:
	virtual void BeginPlay() override;
	virtual void Tick(float DeltaTime) override;
	virtual void SetupPlayerInputComponent(class UInputComponent* PlayerInputComponent) override;
	
	// This needs to tell the engine that this class should replicate its properties to clients (those that are marked with UPROPERTY(Replicated))
	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
	
public: // Components //
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Components")
	UWidgetComponent* Nameplate; // Component attached to the root component that displays the nameplate

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Components")
	USphereComponent* NameplateRange; // Sphere component that handles the visibility of the nameplate based on overlap with pawns

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Components")
	UAttributesComponent* AttributesComponent; // Component that handles the Entity's Attributes

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Components")
	UResourcesComponent* ResourcesComponent; // Component that handles the Entity's Resources

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Components")
	UStatsComponent* StatsComponent; // Component that handles the Entity's Stats

public: // General Properties //
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Setup Required|Core Data")
	FCoreEntityData CoreEntityData; // Contains shared data for all entities. This won't be filled from the DataTable, but rather filled in the Editor

	// Contains data for specific entities (e.g. PlayerCharacterData for PlayerCharacters) - needs to be filled in the Editor
	// Will be used to load the specific data for the entity in the InitializeSpecificEntityCoreData() method from the DataTable
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Setup Required|Data Tables")
	TObjectPtr<UDataTable> SpecificEntityCoreDataTable;

public:
	UPROPERTY(BlueprintReadOnly, Replicated)
	ELifecycleState LifecycleState = ELifecycleState::Active; // Represents whether the entity is Spawning Process, Active or in Death Process
	
	UPROPERTY()
	class UEntityNameplateWidget* NameplateWidget; // Actual instance of the WBP_Nameplate from the WidgetComponent (its WidgetClass) filled in the Unreal Editor

protected: // Initialization //
	void ConfigureGenericMeshProperties(); // Configures generic properties for the entity's skeletal mesh

	void ConfigureCollisionProperties(); // Configures collision properties for the entity's capsule component

	void CreateNameplateComponent(); // Creates and configures the widget component for displaying the entity's nameplate

	void CreateNameplateSphere(); // Creates and configures the sphere component for managing the nameplate's visibility

	void InitializeAssociatedEntityForNameplateWidget(); // Initializes the association between the entity and its corresponding Nameplate widget

	virtual void InitializeSpecificEntityCoreData() PURE_VIRTUAL(ABaseEntity::InitializeCoreData, ); // Initializes the core data struct for the entity - override in derived classes

public: // Methods //
	virtual float GetInteractionRange(const ABaseEntity* Initiator) const; // Returns the interaction range for this entity (from CoreEntityData by default, can be overridden by derived classes)

	float CalculateRealDistanceToEntity(const AActor* Target) const; // Calculates the real distance between this entity and a target entity, accounting for their collision radii
	
	bool IsWithinInteractionRangeOf(const ABaseEntity* Target) const; // Determines if this entity is within interaction range of the target entity

	virtual void Interact(ABaseEntity* Initiator); // Called when this entity is interacted with by another entity - override in derived classes for specific behavior

public: // RPCs //
	UFUNCTION(Server, Reliable)
	void ServerRotateTowardsTarget(const FVector& TargetLocation); // Server RPC to rotate this entity towards target location

	UFUNCTION(Client, Reliable)
	void ClientRotateTowardsTarget(const FVector& TargetLocation); // Client RPC to rotate this entity towards target location

	void RotateTowardsEntity(ABaseEntity* TargetEntity); // Helper function to rotate this entity towards another entity

protected: // Overlap Events //
	/**
	 * Handles the logic triggered when another actor begins overlap with the NameplateRange component.
	 * This typically makes the nameplate visible for locally controlled player characters within the specified range.
	 *
	 * @param OverlappedComponent The component that triggered the begin overlap event.
	 * @param OtherActor The actor that has entered the overlap range.
	 * @param OtherComp The specific component of the other actor involved in the overlap.
	 * @param OtherBodyIndex The index of the body that has overlapped.
	 * @param bFromSweep Indicates whether the overlap was caused by a sweep test.
	 * @param SweepResult Contains information about the sweep test if bFromSweep is true.
	 */
	UFUNCTION()
	void OnNameplateRangeBeginOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);
	
	/**
	 * Handles the logic triggered when another actor ends overlap with the NameplateRange component.
	 * This typically hides the nameplate for locally controlled player characters upon exiting the specified range.
	 *
	 * @param OverlappedComponent The component that triggered the end overlap event.
	 * @param OtherActor The actor that has exited the overlap range.
	 * @param OtherComp The specific component of the other actor involved in the overlap.
	 * @param OtherBodyIndex The index of the body that exited the overlap.
	 */
	UFUNCTION()
	void OnNameplateRangeEndOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex);
};
