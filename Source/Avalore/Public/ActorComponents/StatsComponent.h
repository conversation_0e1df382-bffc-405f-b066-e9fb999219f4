// Copyright (c) 2024, <PERSON>. All rights reserved.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Structs/AvaloreStructs.h"
#include "Structs/AvaloreEnums.h"

#include "StatsComponent.generated.h"

class ABaseEntity;

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class AVALORE_API UStatsComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	// Sets default values for this component's properties
	UStatsComponent();

protected:
	// Called when the game starts
	virtual void BeginPlay() override;

	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
	// Called every frame
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

protected:
	UPROPERTY()
	TObjectPtr<ABaseEntity> OwningEntity;

public:
	UPROPERTY(VisibleDefaultsOnly, BlueprintReadOnly, ReplicatedUsing = OnRep_Stats, Category = "Stats")
	TArray<FStat> Stats;

protected:
	void PopulateStatsArray();

public:
	UFUNCTION()
	void OnRep_Stats();

	// Event Dispatcher for when an attribute's value changes.
	DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnStatUpdated);
	UPROPERTY(BlueprintAssignable, Category = "Event Dispatchers")
	FOnStatUpdated OnStatUpdated;

public:
	void SetStatValue(EStat Stat, EStatComponent Component, float Value);

	float GetStatValue(EStat Stat, EStatComponent Component, bool bIgnoreComponentAndReturnTotal) const;

	void IncreaseStatValue(EStat Stat, EStatComponent Component, float Value);
};
