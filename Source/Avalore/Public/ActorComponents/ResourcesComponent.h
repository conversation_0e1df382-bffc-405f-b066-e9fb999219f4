// Copyright (c) 2024, <PERSON>. All rights reserved.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Structs/AvaloreStructs.h"
#include "Structs/AvaloreEnums.h"

#include "ResourcesComponent.generated.h"

class ABaseEntity;

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class AVALORE_API UResourcesComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	// Sets default values for this component's properties
	UResourcesComponent();

protected:
	// Called when the game starts
	virtual void BeginPlay() override;

	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

public:
	// Called every frame
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

protected:
	UPROPERTY()
	TObjectPtr<ABaseEntity> OwningEntity; // The entity that owns this ResourcesComponent

public:
	UPROPERTY(VisibleDefaultsOnly, BlueprintReadOnly, ReplicatedUsing = OnRep_Resources, Category = "Resources")
	TArray<FResource> Resources; // The resources of the entity

protected:
	FTimerHandle ResourcesRegenerationTickTimerHandle; // Timer handle for the resources regeneration tick

public:
	UFUNCTION()
	void OnRep_Resources(); // Replication function created due to ReplicatedUsing = OnRep_Resources

	// Event Dispatcher for when a Resource's value changes.
	DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnResourceUpdated);
	UPROPERTY(BlueprintAssignable, Category = "Event Dispatchers")
	FOnResourceUpdated OnResourceUpdated;

protected:
	void PopulateResourcesArray();

public:
	void SetResourceValue(EResource Resource, EResourceComponent Component, float Value); // Sets the value of the resource's component to the given value

	float GetResourceValue(EResource Resource, EResourceComponent Component, bool bIgnoreComponentAndReturnTotal) const; // Returns the value of the resource's component (or the total value if 'bIgnoreComponentAndReturnTotal' is true)

	void IncreaseResourceValue(EResource Resource, EResourceComponent Component, float Value); // Increases the value of the resource's component by the given value

	bool IsResourceFull(EResource Resource) const; // Returns true if the resource is full (current value is equal to the total value)

	bool SubtractHealth(float Value); // Subtracts the given value from the health resource with additional checks for potential negative final values. Also returns true if the subtraction was lethal (entity dies).
	void SubtractMana(float Value); // Subtracts the given value from the mana resource with additional checks for potential negative final values
	void SubtractStamina(float Value); // Subtracts the given value from the stamina resource with additional checks for potential negative final values

	// Scales the current value of the resource to the new max value (e.g. when the max health is increased, the current health should be scaled to the new max health)
	void ScaleCurrentValueToNewMaxValue(EResource Resource);

	// Returns true if the current value of the resource should be scaled to the new max value (e.g. when the max health is increased, the current health should be scaled to the new max health)
	bool ShouldScaleCurrentValueToNewMaxValue(EResource Resource) const;

	float GetCurrentHealthAsPercentageForProgressBar() const; // Returns the current health as a percentage for the progress bar (0-1)

	void ResourcesRegenerationTick(); // Regenerates the resources (e.g. health, mana, stamina) based on the regeneration rate per tick

public: // RPCs //
	UFUNCTION(Server, Reliable, BlueprintCallable)
	void ServerSetResourceValue(EResource Resource, EResourceComponent Component, float Value);

	UFUNCTION(Server, Reliable, BlueprintCallable)
	void ServerIncreaseResourceValue(EResource Resource, EResourceComponent Component, float Value);
};
