<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Skin caching code from MS</Name>
  <Location>/Engine/Source/Runtime/Engine/Private/</Location>
  <Date>2016-06-15T18:44:32.7154725-04:00</Date>
  <Function>UE contribution from Microsoft</Function>
  <Justification />
  <Eula>Custom agreement between Epic/Microsoft, refer to Microsoft UE License Amendment 3 in Contraxx</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>None</LicenseFolder>
</TpsData>