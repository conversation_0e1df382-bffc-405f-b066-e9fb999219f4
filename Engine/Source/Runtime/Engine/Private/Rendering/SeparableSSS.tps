<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Separable SSS</Name>
  <Location>/Engine/Source/Runtime/Engine/Private/Rendering/SeparableSSS.cpp</Location>
  <Date>2016-06-15T18:35:23.0645129-04:00</Date>
  <Function>It’s a simple yet efficient method to render realistic skin and other subsurface scattering</Function>
  <Justification>Many licensees asked for the feature. I did some test implementation where it shows good results.  It can improve the Fortnite look without much art effort. </Justification>
  <Eula>https://github.com/iryoku/separable-sss/blob/master/LICENSE.txt</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses/SeparableSSS_License.txt</LicenseFolder>
</TpsData>