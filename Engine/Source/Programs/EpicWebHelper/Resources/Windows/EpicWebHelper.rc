// Copyright Epic Games, Inc. All Rights Reserved.

#include <windows.h>
#include "Runtime/Launch/Resources/Version.h"

/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
 FILEVERSION ENGINE_MAJOR_VERSION,ENGINE_MINOR_VERSION,ENGINE_PATCH_VERSION,0
 PRODUCTVERSION ENGINE_MAJOR_VERSION,ENGINE_MINOR_VERSION,ENGINE_PATCH_VERSION,0
 FILEFLAGSMASK 0x17L
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x4L
 FILETYPE 0x2L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "CompanyName", EPIC_COMPANY_NAME
            VALUE "LegalCopyright", EPIC_COPYRIGHT_STRING
			VALUE "ProductName", EPIC_PRODUCT_NAME
			VALUE "ProductVersion", ENGINE_VERSION_STRING
            VALUE "FileDescription", "EpicWebHelper"
            VALUE "InternalName", "EpicWebHelper"
            VALUE "OriginalFilename", "EpicWebHelper.exe"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END





