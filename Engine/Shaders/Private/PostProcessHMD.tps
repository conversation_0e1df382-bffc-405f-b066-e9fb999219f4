<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Oculus VR SDK</Name>
  <Location>/Engine/Shaders/Private/PostProcessHMD.usf</Location>
  <Date>2016-06-10T17:58:02.2788378-04:00</Date>
  <Function />
  <Justification />
  <Eula>Custom agreement between Epic/Oculus, refer to IPP Agreement in Contraxx</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>None</LicenseFolder>
</TpsData>