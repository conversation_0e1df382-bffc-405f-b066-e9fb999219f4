# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "Types": {
                    "UsdRiMaterialAPI": {
                        "alias": {
                            "UsdSchemaBase": "RiMaterialAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "RiMaterialAPI", 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdRiRenderPassAPI": {
                        "alias": {
                            "UsdSchemaBase": "RiRenderPassAPI"
                        }, 
                        "apiSchemaAutoApplyTo": [
                            "RenderPass"
                        ], 
                        "apiSchemaCanOnlyApplyTo": [
                            "RenderPass"
                        ], 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "RiRenderPassAPI", 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdRiSplineAPI": {
                        "alias": {
                            "UsdSchemaBase": "RiSplineAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "RiSplineAPI", 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdRiStatementsAPI": {
                        "alias": {
                            "UsdSchemaBase": "StatementsAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "StatementsAPI", 
                        "schemaKind": "singleApplyAPI"
                    }
                }
            }, 
            "LibraryPath": "../../../../../Source/ThirdParty/Mac/bin/libusd_usdRi.dylib", 
            "Name": "usdRi", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
