# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "SdfMetadata": {
                    "bindMaterialAs": {
                        "appliesTo": [
                            "relationships"
                        ], 
                        "displayGroup": "Shading", 
                        "documentation": "Metadata authored on collection-based material binding relationship to indicate the strength of the binding relative to bindings authored on descendant prims.", 
                        "type": "token"
                    }, 
                    "connectability": {
                        "appliesTo": [
                            "attributes"
                        ], 
                        "default": "full", 
                        "displayGroup": "Shading", 
                        "documentation": "Metadata authored on UsdShadeInput's to specify what they can be connected to. Can be either \"full\" or \"interfaceOnly\". \"full\" implies that  the input can be connected to any other input or output.  \"interfaceOnly\" implies that the input can only connect to a NodeGraph Input (which represents an interface override, not a render-time dataflow connection), or another Input whose connectability is also \"interfaceOnly\".", 
                        "type": "token"
                    }, 
                    "outputName": {
                        "appliesTo": [
                            "relationships"
                        ], 
                        "displayGroup": "deprecated", 
                        "type": "token"
                    }, 
                    "renderType": {
                        "appliesTo": [
                            "properties"
                        ], 
                        "displayGroup": "Rendering", 
                        "type": "token"
                    }, 
                    "sdrMetadata": {
                        "appliesTo": [
                            "prims", 
                            "attributes"
                        ], 
                        "displayGroup": "Shading", 
                        "type": "dictionary"
                    }
                }, 
                "Types": {
                    "UsdShadeConnectableAPI": {
                        "alias": {
                            "UsdSchemaBase": "ConnectableAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "ConnectableAPI", 
                        "schemaKind": "nonAppliedAPI"
                    }, 
                    "UsdShadeCoordSysAPI": {
                        "alias": {
                            "UsdSchemaBase": "CoordSysAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "CoordSysAPI", 
                        "schemaKind": "multipleApplyAPI"
                    }, 
                    "UsdShadeMaterial": {
                        "alias": {
                            "UsdSchemaBase": "Material"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdShadeNodeGraph"
                        ], 
                        "providesUsdShadeConnectableAPIBehavior": true, 
                        "schemaIdentifier": "Material", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdShadeMaterialBindingAPI": {
                        "alias": {
                            "UsdSchemaBase": "MaterialBindingAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "MaterialBindingAPI", 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdShadeNodeDefAPI": {
                        "alias": {
                            "UsdSchemaBase": "NodeDefAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "NodeDefAPI", 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdShadeNodeGraph": {
                        "alias": {
                            "UsdSchemaBase": "NodeGraph"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdTyped"
                        ], 
                        "providesUsdShadeConnectableAPIBehavior": true, 
                        "schemaIdentifier": "NodeGraph", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdShadeShader": {
                        "alias": {
                            "UsdSchemaBase": "Shader"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdTyped"
                        ], 
                        "providesUsdShadeConnectableAPIBehavior": true, 
                        "schemaIdentifier": "Shader", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdShadeShaderDefParserPlugin": {
                        "bases": [
                            "NdrParserPlugin"
                        ], 
                        "displayName": "USD-based shader definition parser plugin"
                    }
                }
            }, 
            "LibraryPath": "../../../../../Source/ThirdParty/Mac/bin/libusd_usdShade.dylib", 
            "Name": "usdShade", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
