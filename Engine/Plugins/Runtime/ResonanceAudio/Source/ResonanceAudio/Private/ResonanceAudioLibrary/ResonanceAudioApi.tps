<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Resonance Audio API</Name>
  <Location>/Engine/Source/ThirdParty/ResonanceAudioApi/<Location/>
  <Date>2017-10-17T10:49:24+01:00</Date>
  <Function>Library that allows to add immersive spatialized audio and simulate room acoustics</Function>
  <Justification />
  <Eula>Custom agreement between Google/Epic<Eula/>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>None<LicenseFolder/>
</TpsData>