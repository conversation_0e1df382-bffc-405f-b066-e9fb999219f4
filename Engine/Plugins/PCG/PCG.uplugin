{"FileVersion": 3, "Version": 8, "VersionName": "0.2", "FriendlyName": "Procedural Content Generation Framework (PCG)", "Description": "Visual scripting framework for procedurally populating worlds with content in editor and/or at run-time.", "Category": "Editor", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "https://docs.unrealengine.com/latest/en-US/procedural-content-generation--framework-in-unreal-engine/", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": true, "IsBetaVersion": true, "Installed": false, "Modules": [{"Name": "PCG", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "PCGEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "PCGCompute", "Type": "Runtime", "LoadingPhase": "PostConfigInit"}], "Plugins": [{"Name": "EditorScriptingUtilities", "Enabled": true}, {"Name": "ComputeFramework", "Enabled": true}]}